/* 赛博论命 - 主样式文件 */

/* 基础重置和变量 */
:root {
    --primary-color: #00d4ff;
    --secondary-color: #ff0080;
    --accent-color: #00ff88;
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --text-primary: #ffffff;
    --text-secondary: #b8b8b8;
    --text-accent: #00d4ff;
    --border-color: #333;
    --glow-color: rgba(0, 212, 255, 0.5);
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Noto Sans SC', sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-secondary);
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow-x: hidden;
    line-height: 1.6;
}

/* 粒子背景 */
#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
}

/* 导航栏 */
.cyber-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(26, 26, 46, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.logo {
    font-family: var(--font-primary);
    font-weight: 900;
}

.logo-text {
    font-size: 1.5rem;
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--glow-color);
}

.logo-sub {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-left: 0.5rem;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-item {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-item:hover,
.nav-item.active {
    color: var(--primary-color);
    background: rgba(0, 212, 255, 0.1);
    box-shadow: 0 0 10px var(--glow-color);
}

/* 导航配置按钮 */
.nav-config {
    display: flex;
    align-items: center;
}

.config-toggle {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 255, 255, 0.3);
}

.config-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.5);
}

.config-text {
    font-weight: 500;
}

/* 全局配置面板 */
.global-config-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.config-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.config-content {
    position: relative;
    background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 30px;
    max-width: 600px;
    width: 100%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(20px);
}

.config-header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.config-header h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin: 0 0 10px 0;
    text-shadow: 0 0 10px var(--glow-color);
}

.config-header p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.config-close {
    position: absolute;
    top: -10px;
    right: -10px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.config-close:hover {
    background: rgba(255, 0, 0, 0.2);
    color: #ff4444;
}

.config-form {
    margin-bottom: 30px;
}

.config-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.config-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-item label {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

.config-item input,
.config-item select {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.3s ease;
}

.config-item input:focus,
.config-item select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.config-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.status-indicator {
    font-size: 12px;
}

.status-text {
    color: var(--text-secondary);
    font-size: 14px;
}

.config-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.config-actions .cyber-button {
    flex: 1;
    max-width: 200px;
}

.config-actions .cyber-button.secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid var(--border-color);
}

.config-actions .cyber-button.secondary:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1));
}

/* 主容器 */
.main-container {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

.section {
    display: none;
    min-height: calc(100vh - 80px);
    padding: 2rem;
}

.section.active {
    display: block;
}

/* 首页样式 */
.hero-section {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 80px);
    text-align: center;
}

.hero-content {
    max-width: 1000px;
}

.hero-title {
    margin-bottom: 2rem;
}

.title-main {
    display: block;
    font-family: var(--font-primary);
    font-size: 4rem;
    font-weight: 900;
    color: var(--primary-color);
    text-shadow: 0 0 20px var(--glow-color);
    margin-bottom: 0.5rem;
}

.title-sub {
    display: block;
    font-family: var(--font-primary);
    font-size: 1.5rem;
    color: var(--text-secondary);
    letter-spacing: 0.2em;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: rgba(26, 26, 46, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 20px var(--glow-color);
    transform: translateY(-5px);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.feature-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 章节样式 */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    color: var(--primary-color);
    text-shadow: 0 0 15px var(--glow-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.content-container {
    max-width: 1000px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

/* 表单样式 */
.input-panel {
    background: rgba(26, 26, 46, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.cyber-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* 时间输入组件样式 */
.time-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.time-input-group select {
    flex: 1;
    min-width: 80px;
}

.time-hint {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: var(--primary-color);
    opacity: 0.7;
    font-style: italic;
}

/* 真太阳时修正信息样式 */
.true-solar-time-section {
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.true-solar-time-section h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.time-correction-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.time-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.time-row:last-child {
    border-bottom: none;
}

.time-label {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    min-width: 80px;
}

.time-value {
    color: var(--primary-color);
    font-family: 'Courier New', monospace;
}

.time-value.positive {
    color: #00ff88;
}

.time-value.negative {
    color: #ff6b6b;
}

.correction-details {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.correction-details small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85rem;
}

/* 计算方法提示样式 */
.calculation-method-warning {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #ffc107;
    padding: 0.5rem;
    border-radius: 4px;
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

.calculation-method-info {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #28a745;
    padding: 0.5rem;
    border-radius: 4px;
    margin-top: 0.5rem;
    font-size: 0.9rem;
}



.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    color: var(--text-accent);
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group input,
.form-group select {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.75rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 10px var(--glow-color);
}

.radio-group {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.radio-group input[type="radio"] {
    width: auto;
    margin-right: 0.5rem;
}

.radio-group label {
    margin: 0;
    cursor: pointer;
}

/* 按钮样式 */
.cyber-button {
    position: relative;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 4px;
    padding: 1rem 2rem;
    color: white;
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
}

.cyber-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}

.cyber-button span {
    position: relative;
    z-index: 2;
}

.button-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cyber-button:hover .button-glow {
    left: 100%;
}

/* 结果面板 */
.result-panel {
    background: rgba(26, 26, 46, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

/* 结果内容样式 */
.result-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.result-title {
    font-family: var(--font-primary);
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 10px var(--glow-color);
}

.result-info {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 八字命盘样式 */
.bazi-chart {
    margin-bottom: 2rem;
}

.bazi-chart h4 {
    color: var(--text-accent);
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.pillars-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.pillar {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}

.pillar:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 15px var(--glow-color);
    transform: translateY(-2px);
}

.pillar-label {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: bold;
}

.pillar-chars {
    font-family: var(--font-primary);
    font-size: 1.3rem;
    color: var(--primary-color);
    font-weight: bold;
    text-shadow: 0 0 8px var(--glow-color);
    letter-spacing: 1px;
    line-height: 1.2;
}

.pillar-god {
    color: var(--text-accent);
    font-size: 0.8rem;
    font-weight: bold;
    padding: 0.15rem 0.4rem;
    background: rgba(255, 0, 128, 0.1);
    border-radius: 3px;
    border: 1px solid rgba(255, 0, 128, 0.2);
}

.pillar-wuxing {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
    margin-top: 0.2rem;
}

.wuxing-tiangan, .wuxing-dizhi {
    font-size: 0.65rem;
    padding: 0.1rem 0.3rem;
    border-radius: 2px;
    font-weight: normal;
}

.wuxing-tiangan {
    background: rgba(0, 212, 255, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(0, 212, 255, 0.2);
}

.wuxing-dizhi {
    background: rgba(255, 0, 128, 0.1);
    color: var(--accent-color);
    border: 1px solid rgba(255, 0, 128, 0.2);
}

.pillar-nayin {
    font-size: 0.7rem;
    color: #00ff88;
    font-weight: bold;
    padding: 0.15rem 0.4rem;
    background: rgba(0, 255, 136, 0.1);
    border-radius: 3px;
    border: 1px solid rgba(0, 255, 136, 0.2);
    text-shadow: 0 0 3px rgba(0, 255, 136, 0.5);
    margin-top: 0.15rem;
}



/* 大运信息样式 */
.dayun-section {
    margin-bottom: 2rem;
}

.dayun-section h4 {
    color: var(--text-accent);
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.dayun-info p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.dayun-pillars {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
}

.dayun-pillar {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.5rem;
    text-align: center;
    font-size: 0.8rem;
}

.dayun-age {
    color: var(--text-secondary);
    font-size: 0.7rem;
    margin-bottom: 0.25rem;
}

.dayun-chars {
    color: var(--primary-color);
    font-family: var(--font-primary);
    font-weight: bold;
}

/* AI分析样式 */
.ai-analysis {
    margin-bottom: 2rem;
}

.ai-analysis h4 {
    color: var(--text-accent);
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.analysis-prompt {
    margin-bottom: 1rem;
}

.analysis-prompt h5 {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.prompt-content {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.prompt-content pre {
    color: var(--text-primary);
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
}

.analysis-note {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid var(--primary-color);
    border-radius: 4px;
    padding: 1rem;
    margin-top: 1rem;
}

.analysis-note p {
    margin: 0.5rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.analysis-note strong {
    color: var(--primary-color);
}

/* 结果操作按钮 */
.result-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.result-actions .cyber-button {
    flex: 1;
    max-width: 200px;
}

/* 成功提示样式 */
.success-toast {
    font-family: var(--font-secondary);
    box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

/* 表单分组样式 */
.form-section {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section-title {
    color: var(--text-accent);
    font-family: var(--font-primary);
    font-size: 1.1rem;
    margin-bottom: 1rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

/* 隐藏结果面板的默认样式 */
.result-panel[style*="display: none"] {
    display: none !important;
}

/* 起名结果样式 */
.name-suggestions h4 {
    color: var(--text-accent);
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.names-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.name-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.name-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 15px var(--glow-color);
    transform: translateY(-3px);
}

.name-rank {
    position: absolute;
    top: -10px;
    left: -10px;
    background: var(--primary-color);
    color: var(--bg-primary);
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.name-text {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    color: var(--primary-color);
    margin: 0.5rem 0;
    text-shadow: 0 0 5px var(--glow-color);
}

.name-score {
    font-size: 1.2rem;
    color: var(--accent-color);
    font-weight: bold;
    margin: 0.5rem 0;
}

.name-wuxing,
.name-sancai {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0.25rem 0;
}

/* 测名结果样式 */
.score-display {
    text-align: center;
    margin-bottom: 2rem;
}

.score-circle {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    border: 3px solid var(--primary-color);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 212, 255, 0.1), transparent);
    box-shadow: 0 0 20px var(--glow-color);
}

.score-circle.large {
    width: 150px;
    height: 150px;
}

.score-number {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--glow-color);
}

.score-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.analysis-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.detail-section h4 {
    color: var(--text-accent);
    margin-bottom: 0.5rem;
    font-family: var(--font-primary);
}

.wuge-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.5rem;
}

.wuge-item {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.5rem;
    text-align: center;
}

.wuge-label {
    display: block;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.wuge-value {
    display: block;
    font-family: var(--font-primary);
    font-size: 1.2rem;
    color: var(--primary-color);
    font-weight: bold;
}

.analysis-text {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 1rem;
    color: var(--text-primary);
    font-family: var(--font-secondary);
    line-height: 1.6;
    white-space: pre-wrap;
}

/* 合婚结果样式 */
.marriage-analysis {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.match-score {
    text-align: center;
}

.match-level {
    font-family: var(--font-primary);
    font-size: 1.2rem;
    color: var(--accent-color);
    margin-top: 1rem;
    text-shadow: 0 0 5px rgba(0, 255, 136, 0.5);
}

.match-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.match-item {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1.5rem;
}

.match-item h4 {
    color: var(--text-accent);
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.match-score-bar {
    position: relative;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    height: 20px;
    margin: 0.5rem 0;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color), var(--accent-color));
    border-radius: 10px;
    transition: width 1s ease-out;
    position: relative;
}

.score-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.score-text {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-primary);
    font-size: 0.8rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.match-item p {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-top: 0.5rem;
}

.suggestions {
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid var(--primary-color);
    border-radius: 6px;
    padding: 1.5rem;
}

.suggestions h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.suggestions ul {
    list-style: none;
    padding: 0;
}

.suggestions li {
    color: var(--text-secondary);
    margin: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
}

.suggestions li::before {
    content: '💡';
    position: absolute;
    left: 0;
    top: 0;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: var(--text-primary);
}

.cyber-loader {
    width: 60px;
    height: 60px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 紫薇斗数分析样式 */
.ziwei-section {
    background: rgba(138, 43, 226, 0.05);
    border: 1px solid rgba(138, 43, 226, 0.2);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.ziwei-section h4 {
    color: #8a2be2;
    margin-bottom: 1rem;
    text-align: center;
    font-family: var(--font-primary);
}

.ziwei-unavailable {
    text-align: center;
    color: var(--text-secondary);
    padding: 2rem;
}

.ziwei-basic-info {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.info-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.info-value {
    color: #8a2be2;
    font-weight: bold;
    font-size: 0.9rem;
}

.ziwei-analysis {
    margin-bottom: 1.5rem;
}

.ziwei-analysis h5 {
    color: #8a2be2;
    margin-bottom: 0.8rem;
    font-size: 1rem;
}

.analysis-text {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 6px;
    padding: 1rem;
}

.analysis-text pre {
    color: var(--text-primary);
    font-size: 0.9rem;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.ziwei-palaces h5 {
    color: #8a2be2;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.palaces-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.palace-item {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(138, 43, 226, 0.3);
    border-radius: 6px;
    padding: 0.8rem;
    text-align: center;
    transition: all 0.3s ease;
}

.palace-item:hover {
    border-color: #8a2be2;
    box-shadow: 0 0 10px rgba(138, 43, 226, 0.3);
}

.palace-name {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-bottom: 0.3rem;
}

.palace-branch {
    font-family: var(--font-primary);
    font-size: 1rem;
    color: #8a2be2;
    font-weight: bold;
    margin-bottom: 0.3rem;
}

.palace-stars {
    font-size: 0.75rem;
    color: var(--primary-color);
}

.palace-empty {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-style: italic;
}

.ziwei-warning {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
}

.ziwei-warning p {
    color: #ffc107;
    margin: 0;
    font-size: 0.9rem;
}

/* AI分析相关样式 */
.ai-config-section {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.config-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.config-row:last-child {
    margin-bottom: 0;
}

.config-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.config-item label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: bold;
}

.config-item input,
.config-item select {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 0.8rem;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.config-item input:focus,
.config-item select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.config-item input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
}

.analysis-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    justify-content: center;
}

.cyber-button.secondary {
    background: linear-gradient(45deg, #666, #999);
}

.cyber-button.secondary:hover {
    background: linear-gradient(45deg, #777, #aaa);
}

.processing-box {
    background: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 6px;
    padding: 1.5rem;
    margin: 1rem 0;
    text-align: center;
}

.processing-message {
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.processing-steps {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.6;
    text-align: left;
    max-height: 200px;
    overflow-y: auto;
}

.ai-result-section {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 6px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    max-height: none !important;
    overflow: visible !important;
    height: auto !important;
}

.ai-result-section h5 {
    color: #00ff88;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.ai-output {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    padding: 1.5rem;
    color: var(--text-primary);
    line-height: 1.8;
    max-height: none !important;
    overflow-y: visible !important;
    overflow: visible !important;
    height: auto !important;
    font-size: 0.95rem;
}

.ai-output h1,
.ai-output h2,
.ai-output h3 {
    color: var(--primary-color);
    margin: 1.5rem 0 1rem 0;
}

.ai-output h1 {
    font-size: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.ai-output h2 {
    font-size: 1.3rem;
}

.ai-output h3 {
    font-size: 1.1rem;
}

.ai-output strong {
    color: var(--accent-color);
}

.ai-output em {
    color: #00ff88;
    font-style: italic;
}

.api-error-message {
    background: rgba(255, 68, 68, 0.1);
    border: 1px solid rgba(255, 68, 68, 0.3);
    color: #ff4444;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
    text-align: center;
}

.analysis-prompt h5 {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    user-select: none;
}

.analysis-prompt h5:hover {
    color: var(--primary-color);
}

.prompt-content {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    padding: 1rem;
    margin-top: 0.5rem;
}

.prompt-content pre {
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 下载选项样式 */
.download-options {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.download-options .cyber-button {
    flex: 1;
    min-width: 180px;
    max-width: 220px;
}

.download-options .cyber-button.secondary {
    background: linear-gradient(45deg, #666, #999);
    opacity: 0.8;
}

.download-options .cyber-button.secondary:hover {
    background: linear-gradient(45deg, #777, #aaa);
    opacity: 1;
}

.download-note {
    text-align: center;
    margin-top: 1rem;
    color: var(--text-secondary);
    font-size: 0.85rem;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .nav-menu {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }
    
    .title-main {
        font-size: 2.5rem;
    }
    
    .content-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .hero-features {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }

    .pillars-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
    }

    .pillar {
        padding: 0.8rem;
    }

    .pillar-chars {
        font-size: 1.1rem;
        letter-spacing: 0.5px;
    }

    .pillar-god {
        font-size: 0.75rem;
        padding: 0.1rem 0.3rem;
    }

    .wuxing-tiangan, .wuxing-dizhi {
        font-size: 0.6rem;
        padding: 0.08rem 0.25rem;
    }

    .pillar-nayin {
        font-size: 0.65rem;
        padding: 0.1rem 0.3rem;
    }

    .dayun-pillars {
        grid-template-columns: repeat(2, 1fr);
    }

    .names-grid {
        grid-template-columns: 1fr;
    }

    .wuge-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .palaces-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
    }

    .palace-item {
        padding: 0.6rem;
    }

    .ziwei-basic-info {
        padding: 0.8rem;
    }

    .info-row {
        flex-direction: column;
        gap: 0.2rem;
        margin-bottom: 0.8rem;
    }

    .config-row {
        flex-direction: column;
        gap: 1rem;
    }

    .analysis-actions {
        flex-direction: column;
        align-items: center;
    }

    .cyber-button {
        width: 100%;
        max-width: 300px;
    }

    .ai-output {
        font-size: 0.9rem;
        padding: 1rem;
        max-height: none !important;
        overflow-y: visible !important;
        overflow: visible !important;
        height: auto !important;
    }

    .processing-steps {
        font-size: 0.8rem;
    }

    .download-options {
        flex-direction: column;
        align-items: center;
    }

    .download-options .cyber-button {
        width: 100%;
        max-width: 300px;
        min-width: auto;
    }

    .ai-naming-controls {
        flex-direction: column;
    }

    .ai-config {
        grid-template-columns: 1fr;
    }

    .config-row {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .config-row label {
        min-width: auto;
    }

    .bazi-pillars {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
        max-width: 100%;
    }

    .pillar {
        min-height: 100px;
        padding: 0.6rem 0.4rem;
    }

    .pillar-chars {
        font-size: 1.3rem;
    }

    .pillar-wuxing,
    .pillar-god {
        font-size: 0.7rem;
    }

    .wuxing-stats {
        grid-template-columns: repeat(5, 1fr);
        gap: 0.4rem;
        max-width: 100%;
    }

    .wuxing-item {
        min-height: 60px;
        padding: 0.4rem 0.2rem;
    }

    .wuxing-name {
        font-size: 0.8rem;
    }

    .names-grid {
        grid-template-columns: 1fr;
    }

    .ai-naming-output {
        font-size: 0.9rem;
        padding: 1rem;
        max-height: none !important;
        overflow-y: visible !important;
        overflow: visible !important;
        height: auto !important;
    }
}

/* AI起名分析样式 */
.ai-naming-section {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 6px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    max-height: none !important;
    overflow: visible !important;
    height: auto !important;
}

.ai-naming-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.ai-naming-header h4 {
    color: #00ff88;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.ai-naming-header p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin: 0 0 0.8rem 0;
}

.model-recommendation {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 4px;
    padding: 0.6rem 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
}

.rec-icon {
    font-size: 1rem;
}

.rec-text {
    color: var(--text-secondary);
    line-height: 1.4;
}

.rec-text strong {
    color: #00ff88;
    font-weight: bold;
}

.ai-naming-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.ai-config {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

.config-row {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.config-row label {
    min-width: 80px;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.config-row input,
.config-row select {
    flex: 1;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.config-row input:focus,
.config-row select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.ai-naming-processing {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    padding: 1.5rem;
    margin: 1rem 0;
    text-align: center;
}

.ai-naming-processing-message {
    color: var(--primary-color);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.ai-naming-processing-steps {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.6;
    text-align: left;
    max-height: none !important;
    overflow: visible !important;
}

.ai-naming-result-section {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 6px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    max-height: none !important;
    overflow: visible !important;
    height: auto !important;
}

.ai-naming-result-section h5 {
    color: #00ff88;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.ai-naming-output {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    padding: 1.5rem;
    color: var(--text-primary);
    line-height: 1.8;
    max-height: none !important;
    overflow-y: visible !important;
    overflow: visible !important;
    height: auto !important;
    font-size: 0.95rem;
}

.ai-naming-output h1,
.ai-naming-output h2,
.ai-naming-output h3 {
    color: var(--primary-color);
    margin: 1.5rem 0 1rem 0;
}

.ai-naming-output h1 {
    font-size: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.ai-naming-output h2 {
    font-size: 1.3rem;
}

.ai-naming-output h3 {
    font-size: 1.1rem;
}

.ai-naming-output strong {
    color: var(--accent-color);
}

.ai-naming-output em {
    color: #00ff88;
    font-style: italic;
}

/* 合婚AI分析样式 */
.ai-marriage-result-section {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 0, 128, 0.3);
    border-radius: 6px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    max-height: none !important;
    overflow: visible !important;
    height: auto !important;
}

.ai-marriage-result-section h5 {
    color: #ff0080;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    text-shadow: 0 0 10px rgba(255, 0, 128, 0.5);
}

.ai-marriage-output {
    background: linear-gradient(135deg, rgba(255, 0, 128, 0.05), rgba(0, 212, 255, 0.05));
    border: 1px solid rgba(255, 0, 128, 0.2);
    border-radius: 8px;
    padding: 1.5rem;
    color: var(--text-primary);
    line-height: 1.6;
    white-space: pre-wrap;
    max-height: none !important;
    overflow: visible !important;
    height: auto !important;
    font-size: 0.95rem;
}

.ai-marriage-output h1,
.ai-marriage-output h2,
.ai-marriage-output h3,
.ai-marriage-output h4 {
    color: var(--primary-color);
    margin: 1rem 0 0.6rem 0;
    text-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
}

.ai-marriage-output h1 {
    font-size: 1.6rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
    text-align: center;
    margin: 0 0 1rem 0;
}

.ai-marriage-output h2 {
    font-size: 1.4rem;
    border-left: 4px solid var(--accent-color);
    padding-left: 1rem;
    background: rgba(0, 255, 136, 0.1);
    border-radius: 4px;
    margin: 1.2rem 0 0.8rem 0;
}

.ai-marriage-output h3 {
    font-size: 1.2rem;
    color: var(--accent-color);
    margin: 0.8rem 0 0.5rem 0;
}

.ai-marriage-output h4 {
    font-size: 1.1rem;
    color: #ff0080;
    margin: 0.6rem 0 0.4rem 0;
}

.ai-marriage-output strong {
    color: var(--accent-color);
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0, 255, 136, 0.3);
}

.ai-marriage-output em {
    color: #ff0080;
    font-style: italic;
    text-shadow: 0 0 5px rgba(255, 0, 128, 0.3);
}

.ai-marriage-output p {
    margin: 0.6rem 0;
    text-indent: 2em;
}

.ai-marriage-output ul,
.ai-marriage-output ol {
    margin: 0.6rem 0;
    padding-left: 2rem;
}

.ai-marriage-output li {
    margin: 0.3rem 0;
    position: relative;
}

.ai-marriage-output li::marker {
    color: var(--accent-color);
}

/* 合婚AI分析特殊样式 */
.ai-marriage-output .analysis-point {
    background: rgba(0, 255, 136, 0.1);
    border-left: 3px solid var(--accent-color);
    padding: 0.6rem 0.8rem;
    margin: 0.6rem 0;
    border-radius: 0 4px 4px 0;
}

.ai-marriage-output .analysis-point strong {
    display: block;
    margin-bottom: 0.3rem;
    font-size: 1.05rem;
}

.ai-marriage-output .suggestion-box {
    background: rgba(255, 0, 128, 0.1);
    border: 1px solid rgba(255, 0, 128, 0.3);
    border-radius: 8px;
    padding: 0.8rem;
    margin: 0.6rem 0;
}

.ai-marriage-output .highlight {
    background: linear-gradient(90deg, rgba(0, 255, 136, 0.2), transparent);
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    border-left: 2px solid var(--accent-color);
    font-weight: bold;
    color: var(--accent-color);
}

.ai-marriage-output .warning-box {
    background: rgba(255, 165, 0, 0.1);
    border: 1px solid rgba(255, 165, 0, 0.3);
    border-radius: 6px;
    padding: 0.6rem;
    margin: 0.5rem 0;
    color: #ffa500;
    border-left: 4px solid #ffa500;
}

.ai-marriage-output .success-box {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 6px;
    padding: 0.6rem;
    margin: 0.5rem 0;
    color: var(--accent-color);
    border-left: 4px solid var(--accent-color);
}

/* 合婚AI分析处理状态样式 */
.ai-marriage-processing {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 0, 128, 0.3);
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
    text-align: center;
}

.ai-marriage-processing .processing-message {
    color: #ff0080;
    font-weight: bold;
    margin-bottom: 1rem;
}

.ai-marriage-processing .processing-steps {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.6;
}

/* 合婚AI分析响应式样式 */
@media (max-width: 768px) {
    .ai-marriage-output {
        font-size: 0.9rem;
        padding: 1rem;
    }

    .ai-marriage-output h1 {
        font-size: 1.4rem;
    }

    .ai-marriage-output h2 {
        font-size: 1.2rem;
    }

    .ai-marriage-output h3 {
        font-size: 1.1rem;
    }

    .ai-marriage-output .analysis-point {
        padding: 0.6rem 0.8rem;
    }
}

/* 八字信息样式 */
.bazi-info {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 6px;
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.bazi-info h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    text-align: center;
}

.bazi-pillars {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.8rem;
    max-width: 600px;
    margin: 0 auto;
}

.pillar {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.8rem 0.6rem;
    text-align: center;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
}

.pillar-label {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 500;
    line-height: 1;
}

.pillar-chars {
    color: var(--primary-color);
    font-size: 1.6rem;
    font-weight: bold;
    line-height: 1;
    letter-spacing: 0.1em;
    white-space: nowrap;
}

.pillar-wuxing {
    color: #00ff88;
    font-size: 0.8rem;
    font-weight: bold;
    line-height: 1;
    letter-spacing: 0.05em;
    white-space: nowrap;
}

.pillar-god {
    color: var(--accent-color);
    font-size: 0.8rem;
    line-height: 1;
    font-weight: 500;
    white-space: nowrap;
}

/* 五行分析样式 */
.wuxing-analysis {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.wuxing-analysis h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1rem;
}

.wuxing-stats {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.6rem;
    margin-bottom: 1rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.wuxing-item {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.6rem 0.4rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    min-height: 70px;
    justify-content: space-between;
}

.wuxing-name {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: bold;
    line-height: 1;
}

.wuxing-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.wuxing-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transition: width 0.3s ease;
}

.wuxing-count {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: bold;
}

.wuxing-needs {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 4px;
    padding: 0.8rem;
    text-align: center;
}

.needs-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.needs-values {
    color: #00ff88;
    font-weight: bold;
    font-size: 1rem;
}

/* 名字卡片样式增强 */
.names-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.name-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.name-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
    transform: translateY(-2px);
}

.name-rank {
    position: absolute;
    top: -8px;
    left: -8px;
    background: var(--primary-color);
    color: var(--bg-primary);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.name-text {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.name-score {
    color: var(--accent-color);
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.name-details {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.name-wuxing,
.name-sancai,
.name-wuge {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* 自定义字相关样式 */
.custom-chars-info {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 4px;
    padding: 0.8rem;
    margin-bottom: 1rem;
    text-align: center;
}

.custom-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.custom-chars {
    color: #00ff88;
    font-weight: bold;
    font-size: 1rem;
}

.custom-name {
    border-color: rgba(0, 255, 136, 0.5) !important;
    background: rgba(0, 255, 136, 0.05);
}

.custom-name:hover {
    border-color: #00ff88 !important;
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.4);
}

.custom-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #00ff88;
    color: var(--bg-primary);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: bold;
}

/* 自定义字配置样式 */
.custom-chars-config {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1rem;
    margin-top: 0.5rem;
}

.char-position-group {
    margin-bottom: 1rem;
}

.char-position-group:last-child {
    margin-bottom: 0;
}

.position-label {
    display: block;
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
    font-weight: normal;
}

.char-position-group input {
    width: 100%;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.char-position-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.char-position-group small {
    display: block;
    color: var(--text-secondary);
    font-size: 0.8rem;
    line-height: 1.4;
}

/* 自定义配置显示样式 */
.custom-config-display {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.config-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.config-item:last-child {
    margin-bottom: 0;
}

.config-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-right: 0.5rem;
    min-width: 120px;
}

.config-value {
    color: #00ff88;
    font-weight: bold;
    font-size: 1rem;
}
