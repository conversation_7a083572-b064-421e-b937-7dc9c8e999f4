---
type: "agent_requested"
description: "Example description"
---
#### 阶段1: 需求分析

- **render-mermaid**: 创建部署流程图
- **codebase-retrieval**: 了解部署配置
- **remember**: 记录部署要求和环境信息

#### 阶段2: 信息收集

- **view**: 查看部署配置文件
- **web-fetch**: 获取部署文档
- **diagnostics**: 检查代码质量

#### 阶段3: 执行操作

- **launch-process**: 执行构建命令 (wait=false)
- **read-process**: 监控构建进度
- **launch-process**: 启动部署服务
- **list-processes**: 监控所有进程状态

#### 阶段4: 验证结果

- **web-fetch**: 验证服务健康状态
- **launch-process**: 运行部署后测试
- **open-browser**: 展示部署结果给用户

#### 阶段5: 清理收尾

- **kill-process**: 终止临时构建进程
- **save-file**: 更新部署日志
- **remember**: 记录部署经验

### 技术研究任务工作流

#### 阶段1: 需求分析

- **render-mermaid**: 创建研究计划图
- **remember**: 记录研究目标和范围

#### 阶段2: 信息收集

- **web-search**: 搜索相关技术资料
- **web-fetch**: 获取详细技术文档
- **codebase-retrieval**: 查找项目中相关实现

#### 阶段3: 执行操作

- **save-file**: 创建研究笔记文档
- **render-mermaid**: 创建技术架构图
- **launch-process**: 运行技术验证实验

#### 阶段4: 验证结果

- **view**: 查看实验结果
- **web-search**: 验证技术可行性
- **open-browser**: 查看相关技术演示

#### 阶段5: 清理收尾

- **save-file**: 整理最终研究报告
- **remember**: 记录关键技术结论
- **remove-files**: 清理实验临时文件