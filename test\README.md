# 起名计算模块测试文档

## 📋 测试概述

本测试套件用于验证 `js/name-calculator.js` 起名计算模块的各项功能，包括：

- 汉字笔画数计算
- 五行属性判断
- 五格数理计算
- 三才配置分析
- 八字五行需求分析
- 智能起名建议生成
- 姓名综合分析

## 🚀 快速开始

### 方法一：浏览器可视化测试

1. 在浏览器中打开 `test/name-calculator-test.html`
2. 使用界面进行交互式测试
3. 查看实时测试结果

### 方法二：命令行自动化测试

```bash
# 在项目根目录下运行
node test/run-tests.js
```

## 📊 测试功能详解

### 1. 基础功能测试

#### 笔画数计算测试
- **功能**：验证汉字笔画数计算的准确性
- **测试内容**：
  - 常见汉字笔画数（王、李、张等）
  - 生僻字笔画数推算
  - 边界情况处理

#### 五行属性测试
- **功能**：验证汉字五行属性判断
- **测试内容**：
  - 字典中的五行属性
  - 五行属性判断方法
  - 笔画推算五行

#### 五格数理测试
- **功能**：验证五格数理计算
- **测试内容**：
  - 单姓双名五格计算
  - 单姓单名五格计算
  - 复姓双名五格计算
  - 三才配置计算

### 2. 高级功能测试

#### 八字五行分析
- **功能**：分析八字五行需求
- **测试内容**：
  - 五行强弱分析
  - 需要补充的五行
  - 日元五行判断

#### 智能起名建议
- **功能**：生成个性化起名建议
- **测试内容**：
  - 基础起名建议
  - 自定义字起名
  - 候选字库使用
  - 五行匹配优化

#### 姓名综合分析
- **功能**：分析现有姓名
- **测试内容**：
  - 完整姓名分析
  - 五格数理评估
  - 五行匹配度
  - 综合评分

## 🧪 测试用例说明

### 测试数据

#### 常用测试姓氏
- 单姓：王、李、张、刘、陈、杨、赵、黄、周、吴
- 复姓：欧阳、司马、上官、诸葛

#### 常用测试名字
- 男性：小明、华、伟强、志明、建国
- 女性：小红、美丽、雅静、婷婷、晓燕

#### 模拟八字数据
```javascript
{
    yearPillar: '甲子',
    monthPillar: '丙午', 
    dayPillar: '戊申',
    hourPillar: '壬戌',
    dayTianGan: '戊',
    wuxingInfo: {
        year: { tianGan: '木', diZhi: '水' },
        month: { tianGan: '火', diZhi: '火' },
        day: { tianGan: '土', diZhi: '金' },
        hour: { tianGan: '水', diZhi: '土' }
    }
}
```

## 📈 测试结果解读

### 成功标准
- ✅ 所有基础功能测试通过
- ✅ 五格数理计算准确
- ✅ 五行属性判断正确
- ✅ 起名建议生成成功
- ✅ 姓名分析完整

### 常见问题

#### 1. 笔画数不准确
- **原因**：字典中缺少某些汉字
- **解决**：使用笔画推算算法作为备选

#### 2. 五行属性争议
- **原因**：不同流派对五行属性有不同观点
- **解决**：采用主流观点，提供推算备选

#### 3. 起名建议重复
- **原因**：候选字库有限
- **解决**：扩大字库，增加随机性

## 🔧 自定义测试

### 添加新测试用例

1. 在 `run-tests.js` 中添加新的测试套件：

```javascript
{
    name: '新功能测试',
    tests: [
        {
            name: '具体测试项',
            test: () => {
                // 测试逻辑
                const result = nameCalculator.newFunction();
                if (!result) {
                    throw new Error('测试失败');
                }
                return '测试通过';
            }
        }
    ]
}
```

2. 在 `name-calculator-test.html` 中添加新的测试界面

### 修改测试数据

可以在测试文件中修改以下数据：
- `testChars`：测试用汉字
- `testNames`：测试用姓名
- `mockBaziResult`：模拟八字数据

## 📝 测试报告

### 自动化测试报告
运行 `node test/run-tests.js` 后会生成详细的测试报告，包括：
- 各测试套件的通过情况
- 具体失败的测试项
- 总体通过率统计

### 手动测试记录
使用浏览器测试时，建议记录：
- 测试时间
- 测试环境（浏览器版本）
- 发现的问题
- 改进建议

## 🐛 问题反馈

如果在测试过程中发现问题，请记录：
1. 问题描述
2. 复现步骤
3. 期望结果
4. 实际结果
5. 测试环境

## 📚 相关文档

- [起名计算模块文档](../js/name-calculator.js)
- [八字计算模块文档](../js/bazi-calculator.js)
- [项目主文档](../README.md)

---

**注意**：本测试套件主要验证算法逻辑的正确性，实际使用时还需要考虑文化背景、个人喜好等因素。
