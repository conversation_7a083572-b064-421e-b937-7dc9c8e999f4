<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能起名完整流程测试</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            padding: 20px;
            background: #0a0a0a;
            color: #00ff88;
            font-family: 'Courier New', monospace;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(0, 255, 136, 0.05);
            border: 1px solid #00ff88;
            border-radius: 8px;
            padding: 20px;
        }
        .step {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 5px;
        }
        .step-title {
            color: #00ff88;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .step-content {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 4px;
            min-height: 50px;
        }
        .test-button {
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .form-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        .form-group {
            flex: 1;
            min-width: 150px;
        }
        .form-group label {
            display: block;
            margin-bottom: 3px;
            color: #00ff88;
            font-size: 12px;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 5px;
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid #00ff88;
            border-radius: 3px;
            color: #00ff88;
            font-size: 12px;
            box-sizing: border-box;
        }
        .status {
            padding: 8px;
            margin: 5px 0;
            border-radius: 4px;
            font-size: 12px;
        }
        .status.success { background: rgba(0, 255, 136, 0.2); color: #00ff88; }
        .status.error { background: rgba(255, 68, 68, 0.2); color: #ff4444; }
        .status.warning { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
        .status.info { background: rgba(0, 123, 255, 0.2); color: #007bff; }
        
        .ai-result-display {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #00ff88;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 智能起名完整流程测试</h1>
        <p>测试从点击"智能起名"到显示AI分析结果的完整流程</p>
        
        <!-- 步骤1: 配置AI -->
        <div class="step">
            <div class="step-title">步骤1: 配置AI设置</div>
            <div class="form-row">
                <div class="form-group">
                    <label>API地址</label>
                    <input type="text" id="api-url" placeholder="https://api.deepseek.com/v1/chat/completions">
                </div>
                <div class="form-group">
                    <label>API密钥</label>
                    <input type="password" id="api-key" placeholder="sk-...">
                </div>
                <div class="form-group">
                    <label>模型</label>
                    <select id="model">
                        <option value="deepseek-reasoner">DeepSeek-R1</option>
                        <option value="deepseek-chat">DeepSeek-V3</option>
                    </select>
                </div>
            </div>
            <button class="test-button" onclick="saveConfig()">保存配置</button>
            <div class="step-content" id="step1-status">等待配置...</div>
        </div>

        <!-- 步骤2: 填写起名信息 -->
        <div class="step">
            <div class="step-title">步骤2: 填写起名信息</div>
            <div class="form-row">
                <div class="form-group">
                    <label>姓氏</label>
                    <input type="text" id="surname" value="陈">
                </div>
                <div class="form-group">
                    <label>性别</label>
                    <select id="gender">
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>年份</label>
                    <input type="number" id="year" value="1988">
                </div>
                <div class="form-group">
                    <label>月份</label>
                    <input type="number" id="month" value="9">
                </div>
                <div class="form-group">
                    <label>日期</label>
                    <input type="number" id="day" value="25">
                </div>
                <div class="form-group">
                    <label>时辰</label>
                    <select id="hour">
                        <option value="8">辰时(7-9点)</option>
                        <option value="10">巳时(9-11点)</option>
                        <option value="14">未时(13-15点)</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>出生省份</label>
                    <input type="text" id="province" value="浙江省">
                </div>
                <div class="form-group">
                    <label>出生城市</label>
                    <input type="text" id="city" value="杭州市">
                </div>
            </div>
            <div class="step-content" id="step2-status">信息已填写</div>
        </div>

        <!-- 步骤3: 执行智能起名 -->
        <div class="step">
            <div class="step-title">步骤3: 执行智能起名</div>
            <button class="test-button" onclick="executeQiming()">🚀 开始智能起名</button>
            <div class="step-content" id="step3-status">等待执行...</div>
        </div>

        <!-- 步骤4: 显示计算结果 -->
        <div class="step">
            <div class="step-title">步骤4: 八字计算和名字建议</div>
            <div class="step-content" id="step4-status">等待计算结果...</div>
        </div>

        <!-- 步骤5: AI分析过程 -->
        <div class="step">
            <div class="step-title">步骤5: AI分析过程</div>
            <div class="step-content" id="step5-status">等待AI分析...</div>
        </div>

        <!-- 步骤6: AI分析结果 -->
        <div class="step">
            <div class="step-title">步骤6: AI分析结果</div>
            <div class="step-content" id="step6-status">等待AI结果...</div>
            <div class="ai-result-display hidden" id="ai-result-display"></div>
        </div>

        <!-- 调试日志 -->
        <div class="step">
            <div class="step-title">调试日志</div>
            <div class="step-content" id="debug-log" style="height: 200px; overflow-y: auto; font-size: 11px;"></div>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 引入脚本 -->
    <script src="js/bazi-calculator.js"></script>
    <script src="js/name-calculator.js"></script>
    <script src="js/main.js"></script>

    <script>
        let app;
        let currentStep = 1;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff4444' : type === 'success' ? '#00ff88' : type === 'warning' ? '#ffc107' : '#ffffff';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStep(stepNum, message, type = 'info') {
            const stepDiv = document.getElementById(`step${stepNum}-status`);
            stepDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        function saveConfig() {
            const config = {
                apiUrl: document.getElementById('api-url').value,
                apiKey: document.getElementById('api-key').value,
                model: document.getElementById('model').value
            };

            if (!config.apiUrl || !config.apiKey) {
                updateStep(1, '请填写完整的配置信息', 'error');
                log('配置保存失败：信息不完整', 'error');
                return;
            }

            localStorage.setItem('cyberFortune_globalConfig', JSON.stringify(config));
            updateStep(1, `配置已保存: ${config.model}`, 'success');
            log(`配置已保存: ${config.model} @ ${config.apiUrl}`, 'success');
            currentStep = 2;
        }

        async function executeQiming() {
            if (currentStep < 2) {
                updateStep(3, '请先完成AI配置', 'error');
                return;
            }

            log('=== 开始智能起名完整流程测试 ===');
            updateStep(3, '正在执行智能起名...', 'warning');

            try {
                // 初始化应用
                if (!app) {
                    app = new CyberFortuneApp();
                    log('应用实例已创建');
                }

                // 构建起名数据
                const birthData = {
                    surname: document.getElementById('surname').value,
                    gender: document.getElementById('gender').value,
                    year: parseInt(document.getElementById('year').value),
                    month: parseInt(document.getElementById('month').value),
                    day: parseInt(document.getElementById('day').value),
                    hour: parseInt(document.getElementById('hour').value),
                    minute: 0,
                    birthProvince: document.getElementById('province').value,
                    birthCity: document.getElementById('city').value,
                    customConfig: {}
                };

                log(`起名数据: ${birthData.surname}${birthData.gender} ${birthData.year}-${birthData.month}-${birthData.day} ${birthData.hour}时`);

                // 步骤4: 计算八字和生成名字建议
                updateStep(4, '正在计算八字...', 'warning');
                const baziResult = app.baziCalculator.calculate(birthData);
                log(`八字计算完成: ${baziResult.yearPillar} ${baziResult.monthPillar} ${baziResult.dayPillar} ${baziResult.hourPillar}`, 'success');

                const nameSuggestions = app.nameCalculator.generateNameSuggestions(
                    birthData.surname, birthData.gender, baziResult, birthData.customConfig
                );
                log(`生成了 ${nameSuggestions.length} 个名字建议`, 'success');

                const aiPrompt = app.nameCalculator.generateCompleteAINamingPrompt(
                    birthData, baziResult, nameSuggestions, birthData.customConfig
                );
                log(`AI提示词长度: ${aiPrompt.length} 字符`, 'success');

                updateStep(4, `八字: ${baziResult.yearPillar} ${baziResult.monthPillar} ${baziResult.dayPillar} ${baziResult.hourPillar}<br>生成了 ${nameSuggestions.length} 个名字建议`, 'success');

                // 步骤5: 开始AI分析
                updateStep(5, '正在启动AI分析...', 'warning');
                log('开始AI分析...');

                // 监听AI分析过程
                monitorAIAnalysis();

                // 执行AI分析
                await app.generateNamingAIAnalysis(birthData, baziResult, nameSuggestions, aiPrompt);

                updateStep(3, '智能起名流程已启动', 'success');
                log('智能起名流程已启动，等待AI分析完成...');

            } catch (error) {
                const message = `执行失败: ${error.message}`;
                updateStep(3, message, 'error');
                log(message, 'error');
                console.error('执行错误:', error);
            }
        }

        function monitorAIAnalysis() {
            // 监听AI分析元素的变化
            const processingDiv = document.getElementById('ai-naming-processing');
            const resultSection = document.getElementById('ai-naming-result-section');
            const aiOutput = document.getElementById('ai-naming-output');

            if (processingDiv) {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                            if (processingDiv.style.display === 'block') {
                                updateStep(5, 'AI分析正在进行中...', 'warning');
                                log('AI分析处理状态已显示');
                            }
                        }
                    });
                });
                observer.observe(processingDiv, { attributes: true });
            }

            if (resultSection) {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                            if (resultSection.style.display === 'block') {
                                updateStep(5, 'AI分析已完成', 'success');
                                updateStep(6, 'AI分析结果已显示', 'success');
                                log('AI分析结果区域已显示');
                                
                                // 显示AI结果
                                if (aiOutput && aiOutput.innerHTML.trim()) {
                                    const resultDisplay = document.getElementById('ai-result-display');
                                    resultDisplay.innerHTML = aiOutput.innerHTML;
                                    resultDisplay.classList.remove('hidden');
                                    log('AI分析结果已复制到测试页面');
                                }
                            }
                        }
                    });
                });
                observer.observe(resultSection, { attributes: true });
            }

            if (aiOutput) {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList' && aiOutput.innerHTML.trim()) {
                            log('AI输出内容已更新');
                            const resultDisplay = document.getElementById('ai-result-display');
                            resultDisplay.innerHTML = aiOutput.innerHTML;
                            resultDisplay.classList.remove('hidden');
                        }
                    });
                });
                observer.observe(aiOutput, { childList: true, subtree: true });
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('测试页面加载完成');
            
            // 加载现有配置
            const existingConfig = localStorage.getItem('cyberFortune_globalConfig');
            if (existingConfig) {
                try {
                    const config = JSON.parse(existingConfig);
                    document.getElementById('api-url').value = config.apiUrl || '';
                    document.getElementById('api-key').value = config.apiKey || '';
                    document.getElementById('model').value = config.model || 'deepseek-reasoner';
                    updateStep(1, `已加载现有配置: ${config.model}`, 'success');
                    log('已加载现有AI配置', 'success');
                    currentStep = 2;
                } catch (error) {
                    log('加载现有配置失败', 'warning');
                }
            }
        });
    </script>
</body>
</html>
