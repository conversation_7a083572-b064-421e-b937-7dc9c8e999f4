<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>赛博论命 - Cyber Fortune</title>
    <link rel="stylesheet" href="css/style.css?v=20250119">
    <link rel="stylesheet" href="css/animations.css?v=20250119">
    <link rel="stylesheet" href="css/print.css" media="print">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 背景粒子效果 -->
    <div id="particles-js"></div>
    
    <!-- 主导航 -->
    <nav class="cyber-nav">
        <div class="nav-container">
            <div class="logo">
                <span class="logo-text">CYBER</span>
                <span class="logo-sub">FORTUNE</span>
            </div>
            <div class="nav-menu">
                <a href="javascript:void(0)" class="nav-item active" data-section="home">主页</a>
                <a href="javascript:void(0)" class="nav-item" data-section="zhiming">赛博知命</a>
                <a href="javascript:void(0)" class="nav-item" data-section="qiming">赛博起名</a>
                <a href="javascript:void(0)" class="nav-item" data-section="ceming">赛博测名</a>
                <a href="javascript:void(0)" class="nav-item" data-section="hehun">赛博合婚</a>
            </div>
            <div class="nav-config">
                <button class="config-toggle" id="config-toggle">
                    <span>⚙️</span>
                    <span class="config-text">AI配置</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- 全局AI配置面板 -->
    <div class="global-config-panel" id="global-config-panel" style="display: none;">
        <div class="config-overlay" id="config-overlay"></div>
        <div class="config-content">
            <div class="config-header">
                <h3>🤖 全局AI配置</h3>
                <p>配置一次，所有模块共用</p>
                <button class="config-close" id="config-close">✕</button>
            </div>

            <div class="config-form">
                <div class="config-row">
                    <div class="config-item">
                        <label for="global-api-url">API地址:</label>
                        <input type="text" id="global-api-url" placeholder="https://api.deepseek.com/v1/chat/completions"
                               value="https://api.deepseek.com/v1/chat/completions">
                    </div>
                    <div class="config-item">
                        <label for="global-api-key">API密钥:</label>
                        <input type="password" id="global-api-key" placeholder="请输入您的API密钥">
                    </div>
                </div>
                <div class="config-row">
                    <div class="config-item">
                        <label for="global-model">模型:</label>
                        <select id="global-model">
                            <option value="deepseek-r1" selected>DeepSeek-R1 (推荐)</option>
                            <option value="deepseek-chat">DeepSeek-Chat</option>
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                            <option value="claude-3-sonnet">Claude-3 Sonnet</option>
                            <option value="claude-3-haiku">Claude-3 Haiku</option>
                            <option value="qwen-max">通义千问-Max</option>
                            <option value="glm-4">智谱GLM-4</option>
                        </select>
                    </div>
                    <div class="config-item">
                        <label>状态:</label>
                        <div class="config-status" id="config-status">
                            <span class="status-indicator">⚪</span>
                            <span class="status-text">未配置</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="config-actions">
                <button class="cyber-button" id="save-global-config">
                    <span>💾 保存配置</span>
                    <div class="button-glow"></div>
                </button>
                <button class="cyber-button secondary" id="test-global-config">
                    <span>🔍 测试连接</span>
                    <div class="button-glow"></div>
                </button>
            </div>
        </div>
    </div>

    <!-- 主页面 -->
    <main class="main-container">
        <!-- 首页 -->
        <section id="home" class="section active">
            <div class="hero-section">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="title-main">赛博论命</span>
                        <span class="title-sub">CYBER FORTUNE</span>
                    </h1>
                    <p class="hero-description">
                        融合传统命理学与现代AI技术，为您提供精准的命运分析
                    </p>
                    <div class="hero-features">
                        <div class="feature-card" data-target="zhiming">
                            <div class="feature-icon">🔮</div>
                            <h3>赛博知命</h3>
                            <p>八字紫薇综合分析</p>
                        </div>
                        <div class="feature-card" data-target="qiming">
                            <div class="feature-icon">✨</div>
                            <h3>赛博起名</h3>
                            <p>AI智能起名系统</p>
                        </div>
                        <div class="feature-card" data-target="ceming">
                            <div class="feature-icon">📊</div>
                            <h3>赛博测名</h3>
                            <p>姓名综合评分</p>
                        </div>
                        <div class="feature-card" data-target="hehun">
                            <div class="feature-icon">💕</div>
                            <h3>赛博合婚</h3>
                            <p>八字合婚匹配</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 赛博知命 -->
        <section id="zhiming" class="section">
            <div class="section-header">
                <h2 class="section-title">赛博知命</h2>
                <p class="section-subtitle">基于八字与紫薇斗数的综合命理分析</p>
            </div>
            <div class="content-container">
                <div class="input-panel">
                    <form id="zhiming-form" class="cyber-form">
                        <div class="form-group">
                            <label>性别</label>
                            <div class="radio-group">
                                <input type="radio" id="male" name="gender" value="男" checked>
                                <label for="male">男</label>
                                <input type="radio" id="female" name="gender" value="女">
                                <label for="female">女</label>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>出生年份</label>
                                <select name="birthYear" required>
                                    <option value="">选择年份</option>
                                    <!-- 动态生成年份选项 -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生月份</label>
                                <select name="birthMonth" required>
                                    <option value="">选择月份</option>
                                    <!-- 动态生成月份选项 -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生日期</label>
                                <select name="birthDay" required>
                                    <option value="">选择日期</option>
                                    <!-- 动态生成日期选项 -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生时间</label>
                                <div class="time-input-group">
                                    <select name="birthHour" required>
                                        <option value="">时</option>
                                        <option value="0">00时</option>
                                        <option value="1">01时</option>
                                        <option value="2">02时</option>
                                        <option value="3">03时</option>
                                        <option value="4">04时</option>
                                        <option value="5">05时</option>
                                        <option value="6">06时</option>
                                        <option value="7">07时</option>
                                        <option value="8">08时</option>
                                        <option value="9">09时</option>
                                        <option value="10">10时</option>
                                        <option value="11">11时</option>
                                        <option value="12">12时</option>
                                        <option value="13">13时</option>
                                        <option value="14">14时</option>
                                        <option value="15">15时</option>
                                        <option value="16">16时</option>
                                        <option value="17">17时</option>
                                        <option value="18">18时</option>
                                        <option value="19">19时</option>
                                        <option value="20">20时</option>
                                        <option value="21">21时</option>
                                        <option value="22">22时</option>
                                        <option value="23">23时</option>
                                    </select>
                                    <select name="birthMinute" required>
                                        <option value="">分</option>
                                        <option value="0">00分</option>
                                        <option value="5">05分</option>
                                        <option value="10">10分</option>
                                        <option value="15">15分</option>
                                        <option value="20">20分</option>
                                        <option value="25">25分</option>
                                        <option value="30">30分</option>
                                        <option value="35">35分</option>
                                        <option value="40">40分</option>
                                        <option value="45">45分</option>
                                        <option value="50">50分</option>
                                        <option value="55">55分</option>
                                    </select>
                                </div>
                                <small class="time-hint">请选择准确的出生时间，用于真太阳时修正</small>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label>出生省份</label>
                                <select name="birthProvince" required>
                                    <option value="">选择省份</option>
                                    <!-- 动态生成省份选项 -->
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生城市</label>
                                <select name="birthCity" required>
                                    <option value="">选择城市</option>
                                    <!-- 动态生成城市选项 -->
                                </select>
                            </div>
                        </div>
                        
                        <button type="submit" class="cyber-button">
                            <span>开始分析</span>
                            <div class="button-glow"></div>
                        </button>
                    </form>
                </div>
                
                <div class="result-panel" id="zhiming-result" style="display: none;">
                    <div class="result-content">
                        <!-- 分析结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 赛博起名 -->
        <section id="qiming" class="section">
            <div class="section-header">
                <h2 class="section-title">赛博起名</h2>
                <p class="section-subtitle">基于八字五行的AI智能起名系统</p>
            </div>
            <div class="content-container">
                <div class="input-panel">
                    <form id="qiming-form" class="cyber-form">
                        <div class="form-group">
                            <label>姓氏</label>
                            <input type="text" name="surname" placeholder="请输入姓氏" required>
                        </div>

                        <div class="form-group">
                            <label>性别</label>
                            <div class="radio-group">
                                <input type="radio" id="qiming-male" name="gender" value="男" checked>
                                <label for="qiming-male">男</label>
                                <input type="radio" id="qiming-female" name="gender" value="女">
                                <label for="qiming-female">女</label>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>出生年份</label>
                                <select name="birthYear" required>
                                    <option value="">选择年份</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生月份</label>
                                <select name="birthMonth" required>
                                    <option value="">选择月份</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生日期</label>
                                <select name="birthDay" required>
                                    <option value="">选择日期</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生时间</label>
                                <div class="time-input-group">
                                    <select name="birthHour" required>
                                        <option value="">时</option>
                                        <option value="0">00时</option>
                                        <option value="1">01时</option>
                                        <option value="2">02时</option>
                                        <option value="3">03时</option>
                                        <option value="4">04时</option>
                                        <option value="5">05时</option>
                                        <option value="6">06时</option>
                                        <option value="7">07时</option>
                                        <option value="8">08时</option>
                                        <option value="9">09时</option>
                                        <option value="10">10时</option>
                                        <option value="11">11时</option>
                                        <option value="12">12时</option>
                                        <option value="13">13时</option>
                                        <option value="14">14时</option>
                                        <option value="15">15时</option>
                                        <option value="16">16时</option>
                                        <option value="17">17时</option>
                                        <option value="18">18时</option>
                                        <option value="19">19时</option>
                                        <option value="20">20时</option>
                                        <option value="21">21时</option>
                                        <option value="22">22时</option>
                                        <option value="23">23时</option>
                                    </select>
                                    <select name="birthMinute" required>
                                        <option value="">分</option>
                                        <option value="0">00分</option>
                                        <option value="5">05分</option>
                                        <option value="10">10分</option>
                                        <option value="15">15分</option>
                                        <option value="20">20分</option>
                                        <option value="25">25分</option>
                                        <option value="30">30分</option>
                                        <option value="35">35分</option>
                                        <option value="40">40分</option>
                                        <option value="45">45分</option>
                                        <option value="50">50分</option>
                                        <option value="55">55分</option>
                                    </select>
                                </div>
                                <small class="time-hint">请选择准确的出生时间，用于真太阳时修正</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>出生省份</label>
                                <select name="birthProvince" required>
                                    <option value="">选择省份</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生城市</label>
                                <select name="birthCity" required>
                                    <option value="">选择城市</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>自定义用字（可选）</label>
                            <div class="custom-chars-config">
                                <div class="char-position-group">
                                    <label class="position-label">第一个字（辈分字）：</label>
                                    <input type="text" name="firstChar" placeholder="留空表示不指定" maxlength="1">
                                    <small>例如：文（辈分字固定在第一位）</small>
                                </div>
                                <div class="char-position-group">
                                    <label class="position-label">第二个字：</label>
                                    <input type="text" name="secondChar" placeholder="留空表示不指定" maxlength="1">
                                    <small>例如：武（指定第二个字）</small>
                                </div>
                                <div class="char-position-group">
                                    <label class="position-label">候选字库：</label>
                                    <input type="text" name="candidateChars" placeholder="可选字，用逗号分隔">
                                    <small>例如：智,勇,华,强,明,亮（从这些字中选择搭配）</small>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="cyber-button">
                            <span>智能起名</span>
                            <div class="button-glow"></div>
                        </button>
                    </form>
                </div>

                <div class="result-panel" id="qiming-result" style="display: none;">
                    <div class="result-content">
                        <!-- 起名结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 赛博测名 -->
        <section id="ceming" class="section">
            <div class="section-header">
                <h2 class="section-title">赛博测名</h2>
                <p class="section-subtitle">姓名综合评分与详细分析</p>
            </div>
            <div class="content-container">
                <div class="input-panel">
                    <form id="ceming-form" class="cyber-form">
                        <div class="form-group">
                            <label>姓名</label>
                            <input type="text" name="fullName" placeholder="请输入完整姓名" required>
                        </div>

                        <div class="form-group">
                            <label>性别</label>
                            <div class="radio-group">
                                <input type="radio" id="ceming-male" name="gender" value="男" checked>
                                <label for="ceming-male">男</label>
                                <input type="radio" id="ceming-female" name="gender" value="女">
                                <label for="ceming-female">女</label>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>出生年份</label>
                                <select name="birthYear" required>
                                    <option value="">选择年份</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生月份</label>
                                <select name="birthMonth" required>
                                    <option value="">选择月份</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生日期</label>
                                <select name="birthDay" required>
                                    <option value="">选择日期</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生时间</label>
                                <div class="time-input-group">
                                    <select name="birthHour" required>
                                        <option value="">时</option>
                                        <option value="0">00时</option>
                                        <option value="1">01时</option>
                                        <option value="2">02时</option>
                                        <option value="3">03时</option>
                                        <option value="4">04时</option>
                                        <option value="5">05时</option>
                                        <option value="6">06时</option>
                                        <option value="7">07时</option>
                                        <option value="8">08时</option>
                                        <option value="9">09时</option>
                                        <option value="10">10时</option>
                                        <option value="11">11时</option>
                                        <option value="12">12时</option>
                                        <option value="13">13时</option>
                                        <option value="14">14时</option>
                                        <option value="15">15时</option>
                                        <option value="16">16时</option>
                                        <option value="17">17时</option>
                                        <option value="18">18时</option>
                                        <option value="19">19时</option>
                                        <option value="20">20时</option>
                                        <option value="21">21时</option>
                                        <option value="22">22时</option>
                                        <option value="23">23时</option>
                                    </select>
                                    <select name="birthMinute" required>
                                        <option value="">分</option>
                                        <option value="0">00分</option>
                                        <option value="5">05分</option>
                                        <option value="10">10分</option>
                                        <option value="15">15分</option>
                                        <option value="20">20分</option>
                                        <option value="25">25分</option>
                                        <option value="30">30分</option>
                                        <option value="35">35分</option>
                                        <option value="40">40分</option>
                                        <option value="45">45分</option>
                                        <option value="50">50分</option>
                                        <option value="55">55分</option>
                                    </select>
                                </div>
                                <small class="time-hint">请选择准确的出生时间，用于真太阳时修正</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>出生省份</label>
                                <select name="birthProvince" required>
                                    <option value="">选择省份</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>出生城市</label>
                                <select name="birthCity" required>
                                    <option value="">选择城市</option>
                                </select>
                            </div>
                        </div>

                        <button type="submit" class="cyber-button">
                            <span>开始测名</span>
                            <div class="button-glow"></div>
                        </button>
                    </form>
                </div>

                <div class="result-panel" id="ceming-result" style="display: none;">
                    <div class="result-content">
                        <!-- 测名结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </section>

        <!-- 赛博合婚 -->
        <section id="hehun" class="section">
            <div class="section-header">
                <h2 class="section-title">赛博合婚</h2>
                <p class="section-subtitle">八字合婚匹配度分析</p>
            </div>
            <div class="content-container">
                <div class="input-panel">
                    <form id="hehun-form" class="cyber-form">
                        <div class="form-section">
                            <h3 class="form-section-title">男方信息</h3>
                            <div class="form-group">
                                <label>姓名</label>
                                <input type="text" name="maleName" placeholder="请输入男方姓名" required>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>出生年份</label>
                                    <select name="maleBirthYear" required>
                                        <option value="">选择年份</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>出生月份</label>
                                    <select name="maleBirthMonth" required>
                                        <option value="">选择月份</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>出生日期</label>
                                    <select name="maleBirthDay" required>
                                        <option value="">选择日期</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>出生时间</label>
                                    <div class="time-input-group">
                                        <select name="maleBirthHour" required>
                                            <option value="">时</option>
                                            <option value="0">00时</option>
                                            <option value="1">01时</option>
                                            <option value="2">02时</option>
                                            <option value="3">03时</option>
                                            <option value="4">04时</option>
                                            <option value="5">05时</option>
                                            <option value="6">06时</option>
                                            <option value="7">07时</option>
                                            <option value="8">08时</option>
                                            <option value="9">09时</option>
                                            <option value="10">10时</option>
                                            <option value="11">11时</option>
                                            <option value="12">12时</option>
                                            <option value="13">13时</option>
                                            <option value="14">14时</option>
                                            <option value="15">15时</option>
                                            <option value="16">16时</option>
                                            <option value="17">17时</option>
                                            <option value="18">18时</option>
                                            <option value="19">19时</option>
                                            <option value="20">20时</option>
                                            <option value="21">21时</option>
                                            <option value="22">22时</option>
                                            <option value="23">23时</option>
                                        </select>
                                        <select name="maleBirthMinute" required>
                                            <option value="">分</option>
                                            <option value="0">00分</option>
                                            <option value="5">05分</option>
                                            <option value="10">10分</option>
                                            <option value="15">15分</option>
                                            <option value="20">20分</option>
                                            <option value="25">25分</option>
                                            <option value="30">30分</option>
                                            <option value="35">35分</option>
                                            <option value="40">40分</option>
                                            <option value="45">45分</option>
                                            <option value="50">50分</option>
                                            <option value="55">55分</option>
                                        </select>
                                    </div>
                                    <small class="time-hint">请选择准确的出生时间，用于真太阳时修正</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>出生省份</label>
                                    <select name="maleBirthProvince" required>
                                        <option value="">选择省份</option>
                                        <!-- 动态生成省份选项 -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>出生城市</label>
                                    <select name="maleBirthCity" required>
                                        <option value="">选择城市</option>
                                        <!-- 动态生成城市选项 -->
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3 class="form-section-title">女方信息</h3>
                            <div class="form-group">
                                <label>姓名</label>
                                <input type="text" name="femaleName" placeholder="请输入女方姓名" required>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>出生年份</label>
                                    <select name="femaleBirthYear" required>
                                        <option value="">选择年份</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>出生月份</label>
                                    <select name="femaleBirthMonth" required>
                                        <option value="">选择月份</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>出生日期</label>
                                    <select name="femaleBirthDay" required>
                                        <option value="">选择日期</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>出生时间</label>
                                    <div class="time-input-group">
                                        <select name="femaleBirthHour" required>
                                            <option value="">时</option>
                                            <option value="0">00时</option>
                                            <option value="1">01时</option>
                                            <option value="2">02时</option>
                                            <option value="3">03时</option>
                                            <option value="4">04时</option>
                                            <option value="5">05时</option>
                                            <option value="6">06时</option>
                                            <option value="7">07时</option>
                                            <option value="8">08时</option>
                                            <option value="9">09时</option>
                                            <option value="10">10时</option>
                                            <option value="11">11时</option>
                                            <option value="12">12时</option>
                                            <option value="13">13时</option>
                                            <option value="14">14时</option>
                                            <option value="15">15时</option>
                                            <option value="16">16时</option>
                                            <option value="17">17时</option>
                                            <option value="18">18时</option>
                                            <option value="19">19时</option>
                                            <option value="20">20时</option>
                                            <option value="21">21时</option>
                                            <option value="22">22时</option>
                                            <option value="23">23时</option>
                                        </select>
                                        <select name="femaleBirthMinute" required>
                                            <option value="">分</option>
                                            <option value="0">00分</option>
                                            <option value="5">05分</option>
                                            <option value="10">10分</option>
                                            <option value="15">15分</option>
                                            <option value="20">20分</option>
                                            <option value="25">25分</option>
                                            <option value="30">30分</option>
                                            <option value="35">35分</option>
                                            <option value="40">40分</option>
                                            <option value="45">45分</option>
                                            <option value="50">50分</option>
                                            <option value="55">55分</option>
                                        </select>
                                    </div>
                                    <small class="time-hint">请选择准确的出生时间，用于真太阳时修正</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label>出生省份</label>
                                    <select name="femaleBirthProvince" required>
                                        <option value="">选择省份</option>
                                        <!-- 动态生成省份选项 -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>出生城市</label>
                                    <select name="femaleBirthCity" required>
                                        <option value="">选择城市</option>
                                        <!-- 动态生成城市选项 -->
                                    </select>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="cyber-button">
                            <span>开始合婚</span>
                            <div class="button-glow"></div>
                        </button>
                    </form>
                </div>

                <div class="result-panel" id="hehun-result" style="display: none;">
                    <div class="result-content">
                        <!-- 合婚结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 加载动画 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-content">
            <div class="cyber-loader"></div>
            <p>正在分析中...</p>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lunisolar@2.5.1/dist/lunisolar.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/iztro@2.5.3/dist/iztro.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <!-- PDF和图片生成库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"
            onerror="this.onerror=null; this.src='https://unpkg.com/jspdf@2.5.1/dist/jspdf.umd.min.js'"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"
            onerror="this.onerror=null; this.src='https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.min.js'"></script>

    <!-- 库加载检测和备用加载 -->
    <script>
        // 检测并加载html2canvas
        function ensureHtml2Canvas() {
            return new Promise((resolve, reject) => {
                if (typeof html2canvas !== 'undefined') {
                    console.log('html2canvas已加载');
                    resolve();
                    return;
                }

                console.log('html2canvas未加载，尝试备用CDN...');
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js';
                script.onload = () => {
                    console.log('html2canvas备用CDN加载成功');
                    resolve();
                };
                script.onerror = () => {
                    console.error('html2canvas所有CDN都加载失败');
                    reject(new Error('html2canvas库加载失败'));
                };
                document.head.appendChild(script);
            });
        }

        // 检测并加载jsPDF
        function ensureJsPDF() {
            return new Promise((resolve, reject) => {
                if (typeof window.jsPDF !== 'undefined' || typeof jsPDF !== 'undefined') {
                    console.log('jsPDF已加载');
                    resolve();
                    return;
                }

                console.log('jsPDF未加载，尝试备用CDN...');
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/jspdf@2.5.1/dist/jspdf.umd.min.js';
                script.onload = () => {
                    console.log('jsPDF备用CDN加载成功');
                    resolve();
                };
                script.onerror = () => {
                    console.error('jsPDF所有CDN都加载失败');
                    reject(new Error('jsPDF库加载失败'));
                };
                document.head.appendChild(script);
            });
        }

        // 全局库检测函数
        window.ensureLibrariesLoaded = async function() {
            try {
                await Promise.all([ensureHtml2Canvas(), ensureJsPDF()]);
                console.log('所有必需的库都已加载');
                return true;
            } catch (error) {
                console.error('库加载失败:', error);
                return false;
            }
        };
    </script>
    <script src="js/particles-config.js?v=20250119b"></script>
    <script src="js/bazi-calculator.js?v=20250119b"></script>
    <script src="js/ziwei-calculator.js?v=20250119b"></script>
    <script src="js/name-calculator.js?v=20250119b"></script>
    <script src="js/marriage-calculator.js?v=20250119b"></script>
    <script src="js/main.js?v=20250119b"></script>
</body>
</html>
