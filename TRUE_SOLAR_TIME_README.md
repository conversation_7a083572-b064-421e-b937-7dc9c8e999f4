# 真太阳时修正功能说明

## 功能概述

本系统已集成真太阳时修正功能，用于提高八字计算的准确性。真太阳时修正考虑了以下因素：

1. **经度修正**：根据出生地经度与北京时间标准经度（东经120°）的差异进行修正
2. **时间方程修正**：考虑地球公转轨道椭圆性和地轴倾斜导致的太阳时与平均太阳时的差异

## 技术实现

### 1. 经度数据库
- 包含中国所有省市的精确经度数据
- 覆盖34个省级行政区，300+个地级市
- 数据精度：小数点后1位（约11公里精度）

### 2. 修正算法
```javascript
// 经度时差修正（每度4分钟）
const longitudeCorrection = (longitude - 120.0) * 4;

// 时间方程计算（简化版本）
const timeEquation = calculateTimeEquation(julianDay);

// 总修正时间
const totalCorrection = longitudeCorrection + timeEquation;
```

### 3. 界面改进
- 将时辰选择改为精确的小时+分钟输入
- 分钟选择精度：5分钟间隔
- 添加真太阳时修正信息显示

## 使用说明

### 1. 输入要求
- **出生年月日**：必填
- **出生时间**：精确到分钟（建议选择准确时间）
- **出生地点**：省份+城市（用于获取经度）

### 2. 修正信息显示
系统会在结果中显示：
- 原始时间
- 修正后时间
- 出生地经度
- 总修正时间（分钟）
- 经度修正和时间方程的分别贡献

### 3. 修正范围
- **经度修正**：-40分钟到+36分钟
  - 新疆西部：约-40分钟
  - 黑龙江东部：约+36分钟
- **时间方程**：-16分钟到+14分钟
  - 年内变化，11月初最大负值，2月中最大正值

## 典型修正示例

| 城市 | 经度 | 经度修正 | 时间方程 | 总修正 |
|------|------|----------|----------|--------|
| 北京 | 116.4°E | -14.4分钟 | 变化 | -14.4±16分钟 |
| 上海 | 121.4°E | +5.6分钟 | 变化 | +5.6±16分钟 |
| 台北 | 121.5°E | +6.0分钟 | 变化 | +6.0±16分钟 |
| 高雄 | 120.3°E | +1.2分钟 | 变化 | +1.2±16分钟 |
| 乌鲁木齐 | 87.6°E | -129.6分钟 | 变化 | -129.6±16分钟 |
| 拉萨 | 91.1°E | -115.6分钟 | 变化 | -115.6±16分钟 |

## 注意事项

1. **时间精度**：建议使用准确的出生时间，误差在15分钟内
2. **地点选择**：选择最接近实际出生地的城市
3. **历史时区**：本系统使用现代中国标准时间，不考虑历史时区变化
4. **夏令时**：中国大陆1986-1991年实行过夏令时，本系统未考虑此因素

## 技术细节

### 时间方程计算
使用简化的天体力学公式：
- 基于儒略日计算
- 考虑地球轨道偏心率
- 考虑地轴倾斜角度

### 跨日处理
当修正后时间跨越日期边界时：
- 自动调整日期
- 重新计算八字
- 保持农历计算的准确性

### 性能优化
- 经度数据预加载
- 计算结果缓存
- 异常处理机制

## 测试验证

可以使用 `test_true_solar_time.html` 页面进行功能测试：
1. 测试不同城市的修正效果
2. 验证计算结果的合理性
3. 检查边界情况处理

## 未来改进

1. **更精确的时间方程**：使用VSOP87等高精度算法
2. **历史时区支持**：考虑不同年代的时区变化
3. **夏令时处理**：添加历史夏令时的修正
4. **更多地点**：扩展到县级市和特殊地区
