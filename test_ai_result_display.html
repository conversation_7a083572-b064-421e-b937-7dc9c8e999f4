<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI结果显示测试</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            padding: 20px;
            background: #0a0a0a;
            color: #00ff88;
            font-family: 'Courier New', monospace;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0, 255, 136, 0.05);
            border: 1px solid #00ff88;
            border-radius: 8px;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 5px;
        }
        .test-button {
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .element-status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            font-size: 12px;
            background: rgba(0, 0, 0, 0.5);
        }
        .element-status.found {
            border: 1px solid #00ff88;
            color: #00ff88;
        }
        .element-status.not-found {
            border: 1px solid #ff4444;
            color: #ff4444;
        }
        .log-area {
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid #00ff88;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 AI结果显示测试</h1>
        <p>专门测试AI分析结果是否能正确显示</p>
        
        <!-- 元素检查 -->
        <div class="test-section">
            <h3>1. 检查必要的DOM元素</h3>
            <button class="test-button" onclick="checkElements()">检查元素</button>
            <div id="element-check-results"></div>
        </div>

        <!-- 模拟AI分析 -->
        <div class="test-section">
            <h3>2. 模拟AI分析过程</h3>
            <button class="test-button" onclick="simulateAIAnalysis()">模拟AI分析</button>
            <button class="test-button" onclick="simulateAIError()">模拟AI错误</button>
            <div id="simulation-results"></div>
        </div>

        <!-- 实际AI测试 -->
        <div class="test-section">
            <h3>3. 实际AI分析测试</h3>
            <div style="margin-bottom: 10px;">
                <input type="text" id="test-api-url" placeholder="API地址" style="width: 300px; padding: 5px; margin-right: 5px;">
                <input type="password" id="test-api-key" placeholder="API密钥" style="width: 200px; padding: 5px; margin-right: 5px;">
                <select id="test-model" style="padding: 5px;">
                    <option value="deepseek-reasoner">DeepSeek-R1</option>
                    <option value="deepseek-chat">DeepSeek-V3</option>
                </select>
            </div>
            <button class="test-button" onclick="testRealAI()">测试真实AI</button>
            <div id="real-ai-results"></div>
        </div>

        <!-- 日志 -->
        <div class="test-section">
            <h3>4. 测试日志</h3>
            <div id="test-log" class="log-area"></div>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>

        <!-- 模拟的AI分析区域 -->
        <div class="test-section">
            <h3>5. 模拟AI分析区域</h3>
            <div class="ai-naming-section">
                <div class="ai-naming-processing" id="ai-naming-processing" style="display: none;">
                    <div class="processing-message" id="ai-naming-processing-message">正在初始化AI分析...</div>
                    <div class="processing-steps" id="ai-naming-processing-steps"></div>
                </div>
                
                <div class="ai-naming-result-section" id="ai-naming-result-section" style="display: none;">
                    <h5>AI分析结果：</h5>
                    <div class="ai-naming-output" id="ai-naming-output"></div>
                    <div class="result-actions">
                        <button class="cyber-button" id="copy-ai-naming-result" style="display: none;">
                            <span>📄 复制分析结果</span>
                        </button>
                    </div>
                </div>
                
                <div class="api-error-message" id="ai-naming-error-message" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- 引入脚本 -->
    <script src="js/bazi-calculator.js"></script>
    <script src="js/name-calculator.js"></script>
    <script src="js/main.js"></script>

    <script>
        let app;

        function log(message, type = 'info') {
            const logArea = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff4444' : type === 'success' ? '#00ff88' : type === 'warning' ? '#ffc107' : '#ffffff';
            logArea.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function checkElements() {
            const elements = [
                'ai-naming-processing',
                'ai-naming-processing-message',
                'ai-naming-processing-steps',
                'ai-naming-result-section',
                'ai-naming-output',
                'copy-ai-naming-result',
                'ai-naming-error-message'
            ];

            const resultsDiv = document.getElementById('element-check-results');
            resultsDiv.innerHTML = '';

            log('开始检查DOM元素...');

            elements.forEach(id => {
                const element = document.getElementById(id);
                const status = element ? 'found' : 'not-found';
                const statusText = element ? '✅ 找到' : '❌ 未找到';
                
                resultsDiv.innerHTML += `<div class="element-status ${status}">${id}: ${statusText}</div>`;
                log(`元素 ${id}: ${statusText}`, element ? 'success' : 'error');
            });
        }

        function simulateAIAnalysis() {
            log('开始模拟AI分析过程...');
            
            if (!app) {
                app = new CyberFortuneApp();
            }

            // 显示处理状态
            app.showAINamingProcessing();
            log('显示处理状态', 'success');

            // 模拟处理过程
            const processingSteps = document.getElementById('ai-naming-processing-steps');
            const processingMessage = document.getElementById('ai-naming-processing-message');
            const aiOutput = document.getElementById('ai-naming-output');
            const aiResultSection = document.getElementById('ai-naming-result-section');
            const copyBtn = document.getElementById('copy-ai-naming-result');

            setTimeout(() => {
                processingSteps.innerHTML = '🔗 正在连接AI服务器...<br>';
                processingMessage.textContent = '建立连接中...';
                log('模拟连接状态', 'info');
            }, 500);

            setTimeout(() => {
                processingSteps.innerHTML += '🤖 AI正在分析起名方案...<br>';
                processingMessage.textContent = '正在生成分析结果...';
                
                // 显示结果区域
                aiResultSection.style.display = 'block';
                log('显示结果区域', 'success');
                
                // 模拟AI输出
                const mockResult = `
# 智能起名分析报告

## 姓名推荐分析

### 1. 陈明轩
- **传统评分**: 92分
- **五行匹配**: 火、金、土
- **字义分析**: 
  - "明"字出自《诗经·大雅·烝民》"既明且哲，以保其身"，寓意聪明睿智
  - "轩"字意为高远、气宇轩昂，象征志向远大

### 2. 陈思远
- **传统评分**: 89分  
- **五行匹配**: 金、土
- **字义分析**:
  - "思"字代表思考、智慧
  - "远"字寓意志向远大、前程似锦

## 综合建议
根据八字分析，建议选择五行属火、金的字，以平衡命理。推荐"陈明轩"作为首选。
                `;
                
                aiOutput.innerHTML = app.simpleMarkdownParse(mockResult);
                log('模拟AI输出内容', 'success');
            }, 1500);

            setTimeout(() => {
                processingSteps.innerHTML += '✅ AI起名分析完成<br>';
                processingMessage.textContent = '分析完成！';
                
                // 显示复制按钮
                copyBtn.style.display = 'block';
                
                // 隐藏处理状态，显示结果
                app.hideAINamingProcessing();
                log('AI分析模拟完成', 'success');
                
                document.getElementById('simulation-results').innerHTML = '<div style="color: #00ff88;">✅ AI分析模拟成功完成</div>';
            }, 2500);
        }

        function simulateAIError() {
            log('开始模拟AI错误...');
            
            if (!app) {
                app = new CyberFortuneApp();
            }

            // 显示处理状态
            app.showAINamingProcessing();
            
            setTimeout(() => {
                app.showAINamingError('模拟的API连接失败');
                log('显示错误信息', 'error');
                
                document.getElementById('simulation-results').innerHTML = '<div style="color: #ff4444;">❌ AI错误模拟完成</div>';
            }, 1000);
        }

        async function testRealAI() {
            const apiUrl = document.getElementById('test-api-url').value;
            const apiKey = document.getElementById('test-api-key').value;
            const model = document.getElementById('test-model').value;

            if (!apiUrl || !apiKey) {
                log('请填写API配置', 'error');
                return;
            }

            // 保存配置
            const config = { apiUrl, apiKey, model };
            localStorage.setItem('cyberFortune_globalConfig', JSON.stringify(config));
            log(`配置已保存: ${model}`, 'success');

            if (!app) {
                app = new CyberFortuneApp();
            }

            // 构建测试数据
            const birthData = {
                surname: '测试',
                gender: '男',
                year: 1990,
                month: 5,
                day: 15,
                hour: 10,
                minute: 0,
                birthProvince: '北京市',
                birthCity: '北京市',
                customConfig: {}
            };

            try {
                log('开始真实AI测试...');
                
                const baziResult = app.baziCalculator.calculate(birthData);
                const nameSuggestions = app.nameCalculator.generateNameSuggestions(
                    birthData.surname, birthData.gender, baziResult, birthData.customConfig
                );
                const aiPrompt = app.nameCalculator.generateCompleteAINamingPrompt(
                    birthData, baziResult, nameSuggestions, birthData.customConfig
                );

                await app.generateNamingAIAnalysis(birthData, baziResult, nameSuggestions, aiPrompt);
                
                document.getElementById('real-ai-results').innerHTML = '<div style="color: #00ff88;">✅ 真实AI测试已启动</div>';
                log('真实AI测试已启动', 'success');

            } catch (error) {
                const message = `真实AI测试失败: ${error.message}`;
                document.getElementById('real-ai-results').innerHTML = `<div style="color: #ff4444;">❌ ${message}</div>`;
                log(message, 'error');
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('AI结果显示测试页面加载完成');
            
            // 加载现有配置
            const existingConfig = localStorage.getItem('cyberFortune_globalConfig');
            if (existingConfig) {
                try {
                    const config = JSON.parse(existingConfig);
                    document.getElementById('test-api-url').value = config.apiUrl || '';
                    document.getElementById('test-api-key').value = config.apiKey || '';
                    document.getElementById('test-model').value = config.model || 'deepseek-reasoner';
                    log('已加载现有配置', 'success');
                } catch (error) {
                    log('加载配置失败', 'warning');
                }
            }

            // 自动检查元素
            setTimeout(checkElements, 500);
        });
    </script>
</body>
</html>
