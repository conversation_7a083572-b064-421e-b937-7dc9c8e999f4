<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能起名修复测试</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        body {
            padding: 20px;
            background: #0a0a0a;
            color: #00ff88;
            font-family: 'Courier New', monospace;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(0, 255, 136, 0.05);
            border: 1px solid #00ff88;
            border-radius: 8px;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 5px;
        }
        .test-button {
            background: linear-gradient(45deg, #00ff88, #00cc6a);
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: rgba(0, 255, 136, 0.2);
            border: 1px solid #00ff88;
            color: #00ff88;
        }
        .status.error {
            background: rgba(255, 68, 68, 0.2);
            border: 1px solid #ff4444;
            color: #ff4444;
        }
        .status.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            color: #ffc107;
        }
        .form-group {
            margin-bottom: 10px;
        }
        .form-group label {
            display: inline-block;
            width: 80px;
            color: #00ff88;
        }
        .form-group input, .form-group select {
            padding: 5px;
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid #00ff88;
            border-radius: 3px;
            color: #00ff88;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 智能起名AI自动提交修复测试</h1>
        
        <!-- 配置区域 -->
        <div class="test-section">
            <h3>1. AI配置</h3>
            <div class="form-group">
                <label>API地址:</label>
                <input type="text" id="api-url" placeholder="https://api.deepseek.com/v1/chat/completions" style="width: 400px;">
            </div>
            <div class="form-group">
                <label>API密钥:</label>
                <input type="password" id="api-key" placeholder="sk-..." style="width: 300px;">
            </div>
            <div class="form-group">
                <label>模型:</label>
                <select id="model">
                    <option value="deepseek-reasoner">DeepSeek-R1</option>
                    <option value="deepseek-chat">DeepSeek-V3</option>
                </select>
            </div>
            <button class="test-button" onclick="saveConfig()">保存配置</button>
            <button class="test-button" onclick="testConfigOnly()">仅测试配置</button>
            <div id="config-status"></div>
        </div>

        <!-- 测试区域 -->
        <div class="test-section">
            <h3>2. 智能起名测试</h3>
            <div class="form-group">
                <label>姓氏:</label>
                <input type="text" id="surname" value="王">
                <label>性别:</label>
                <select id="gender">
                    <option value="男">男</option>
                    <option value="女">女</option>
                </select>
            </div>
            <div class="form-group">
                <label>出生:</label>
                <input type="number" id="year" value="1992" style="width: 60px;">年
                <input type="number" id="month" value="6" style="width: 40px;">月
                <input type="number" id="day" value="10" style="width: 40px;">日
                <select id="hour">
                    <option value="10">巳时</option>
                    <option value="14">未时</option>
                </select>
            </div>
            <div class="form-group">
                <label>地点:</label>
                <input type="text" id="province" value="江苏省">
                <input type="text" id="city" value="南京市">
            </div>
            <button class="test-button" onclick="testWithoutConfig()">测试无配置情况</button>
            <button class="test-button" onclick="testWithConfig()">测试有配置情况</button>
            <div id="test-status"></div>
        </div>

        <!-- 结果显示区域 -->
        <div class="test-section">
            <h3>3. 测试结果</h3>
            <div id="result-display" style="background: rgba(0,0,0,0.5); padding: 15px; border-radius: 5px; min-height: 200px;">
                等待测试...
            </div>
        </div>
    </div>

    <!-- 引入脚本 -->
    <script src="js/bazi-calculator.js"></script>
    <script src="js/name-calculator.js"></script>
    <script src="js/main.js"></script>

    <script>
        let app;

        function log(message) {
            const resultDiv = document.getElementById('result-display');
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
            console.log(message);
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function saveConfig() {
            const config = {
                apiUrl: document.getElementById('api-url').value,
                apiKey: document.getElementById('api-key').value,
                model: document.getElementById('model').value
            };

            if (!config.apiUrl || !config.apiKey) {
                showStatus('config-status', '请填写完整的配置信息', 'error');
                return;
            }

            localStorage.setItem('cyberFortune_globalConfig', JSON.stringify(config));
            showStatus('config-status', '配置已保存', 'success');
            log(`✅ 配置已保存: ${config.model}`);
        }

        function testConfigOnly() {
            if (!app) {
                app = new CyberFortuneApp();
            }

            const config = app.getGlobalConfig();
            if (config) {
                showStatus('config-status', '配置读取成功', 'success');
                log(`✅ 配置读取成功: ${config.model} @ ${config.apiUrl.substring(0, 30)}...`);
            } else {
                showStatus('config-status', '配置读取失败', 'error');
                log('❌ 配置读取失败');
            }
        }

        async function testWithoutConfig() {
            log('=== 测试无配置情况 ===');
            
            // 清除配置
            localStorage.removeItem('cyberFortune_globalConfig');
            
            if (!app) {
                app = new CyberFortuneApp();
            }

            const birthData = getBirthData();
            
            try {
                showStatus('test-status', '正在测试无配置情况...', 'warning');
                
                const baziResult = app.baziCalculator.calculate(birthData);
                const nameSuggestions = app.nameCalculator.generateNameSuggestions(
                    birthData.surname, birthData.gender, baziResult, birthData.customConfig
                );
                const aiPrompt = app.nameCalculator.generateCompleteAINamingPrompt(
                    birthData, baziResult, nameSuggestions, birthData.customConfig
                );

                log('📊 基础计算完成，开始测试AI分析...');
                await app.generateNamingAIAnalysis(birthData, baziResult, nameSuggestions, aiPrompt);
                
                showStatus('test-status', '无配置测试完成', 'success');
                log('✅ 无配置测试完成 - 应该显示配置错误');

            } catch (error) {
                showStatus('test-status', `测试出错: ${error.message}`, 'error');
                log(`❌ 测试出错: ${error.message}`);
            }
        }

        async function testWithConfig() {
            log('=== 测试有配置情况 ===');
            
            if (!app) {
                app = new CyberFortuneApp();
            }

            const config = app.getGlobalConfig();
            if (!config) {
                showStatus('test-status', '请先保存配置', 'error');
                log('❌ 请先保存配置');
                return;
            }

            const birthData = getBirthData();
            
            try {
                showStatus('test-status', '正在测试有配置情况...', 'warning');
                
                const baziResult = app.baziCalculator.calculate(birthData);
                log(`📊 八字: ${baziResult.yearPillar} ${baziResult.monthPillar} ${baziResult.dayPillar} ${baziResult.hourPillar}`);
                
                const nameSuggestions = app.nameCalculator.generateNameSuggestions(
                    birthData.surname, birthData.gender, baziResult, birthData.customConfig
                );
                log(`📝 生成了 ${nameSuggestions.length} 个名字建议`);
                
                const aiPrompt = app.nameCalculator.generateCompleteAINamingPrompt(
                    birthData, baziResult, nameSuggestions, birthData.customConfig
                );
                log(`📋 AI提示词长度: ${aiPrompt.length} 字符`);

                log('🤖 开始AI分析...');
                await app.generateNamingAIAnalysis(birthData, baziResult, nameSuggestions, aiPrompt);
                
                showStatus('test-status', '有配置测试完成', 'success');
                log('✅ 有配置测试完成 - AI分析应该已启动');

            } catch (error) {
                showStatus('test-status', `测试出错: ${error.message}`, 'error');
                log(`❌ 测试出错: ${error.message}`);
            }
        }

        function getBirthData() {
            return {
                surname: document.getElementById('surname').value,
                gender: document.getElementById('gender').value,
                year: parseInt(document.getElementById('year').value),
                month: parseInt(document.getElementById('month').value),
                day: parseInt(document.getElementById('day').value),
                hour: parseInt(document.getElementById('hour').value),
                minute: 0,
                birthProvince: document.getElementById('province').value,
                birthCity: document.getElementById('city').value,
                customConfig: {}
            };
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 测试页面加载完成');
            
            // 加载现有配置
            const existingConfig = localStorage.getItem('cyberFortune_globalConfig');
            if (existingConfig) {
                try {
                    const config = JSON.parse(existingConfig);
                    document.getElementById('api-url').value = config.apiUrl || '';
                    document.getElementById('api-key').value = config.apiKey || '';
                    document.getElementById('model').value = config.model || 'deepseek-reasoner';
                    log('📋 已加载现有配置');
                } catch (error) {
                    log('⚠️ 加载现有配置失败');
                }
            }

            // 初始化应用
            try {
                app = new CyberFortuneApp();
                log('✅ 应用初始化成功');
            } catch (error) {
                log(`❌ 应用初始化失败: ${error.message}`);
            }
        });
    </script>
</body>
</html>
