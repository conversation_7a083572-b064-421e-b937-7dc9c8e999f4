<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>起名分值一致性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .results {
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>起名分值一致性测试</h1>
        <p>此测试用于验证起名和测名模块计算结果的一致性</p>
        
        <div>
            <button onclick="runConsistencyTest()">运行一致性测试</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results" class="results"></div>
    </div>

    <script src="js/bazi-calculator.js"></script>
    <script src="js/name-calculator.js"></script>
    
    <script>
        let testResults = [];
        
        function runConsistencyTest() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="info">正在运行测试...</div>';

            // 创建计算器实例
            const baziCalculator = new BaziCalculator();
            const nameCalculator = new NameCalculator();

            // 测试数据
            const testCases = [
                {
                    name: '张伟',
                    birthData: {
                        year: 1990,
                        month: 5,
                        day: 15,
                        hour: 10,
                        gender: '男',
                        surname: '张'
                    }
                },
                {
                    name: '李娜',
                    birthData: {
                        year: 1985,
                        month: 8,
                        day: 20,
                        hour: 14,
                        gender: '女',
                        surname: '李'
                    }
                },
                {
                    name: '王小明',
                    birthData: {
                        year: 1995,
                        month: 3,
                        day: 10,
                        hour: 8,
                        gender: '男',
                        surname: '王'
                    }
                }
            ];
            
            let allTestsPassed = true;
            let testResultsHtml = '<h3>测试结果</h3>';
            
            testCases.forEach((testCase, index) => {
                try {
                    // 计算八字
                    const baziResult = baziCalculator.calculate(testCase.birthData);

                    // 多次运行相同的计算，检查结果是否一致
                    const iterations = 10;
                    const results = [];
                    const detailedResults = [];

                    for (let i = 0; i < iterations; i++) {
                        // 测试现有姓名分析
                        const nameAnalysis = nameCalculator.analyzeName(testCase.name, baziResult);

                        // 记录详细信息用于调试
                        const neededWuXing = nameCalculator.analyzeBaziWuXing(baziResult);
                        const nameWuXing = nameCalculator.getNameWuXing(testCase.name.slice(1));

                        detailedResults.push({
                            iteration: i + 1,
                            score: nameAnalysis.score,
                            wuXingMatch: nameAnalysis.wuXingMatch,
                            neededWuXing: neededWuXing.join(','),
                            nameWuXing: JSON.stringify(nameWuXing),
                            wuGe: JSON.stringify(nameAnalysis.wuGe),
                            sanCai: JSON.stringify(nameAnalysis.sanCai)
                        });

                        results.push({
                            nameAnalysis: JSON.stringify(nameAnalysis)
                        });
                    }
                    
                    // 检查所有结果是否一致
                    const firstResult = results[0];
                    let isConsistent = true;
                    const inconsistentFields = [];

                    for (let i = 1; i < results.length; i++) {
                        if (results[i].nameAnalysis !== firstResult.nameAnalysis) {
                            isConsistent = false;
                            // 找出具体哪些字段不一致
                            const first = JSON.parse(firstResult.nameAnalysis);
                            const current = JSON.parse(results[i].nameAnalysis);

                            if (first.score !== current.score) {
                                inconsistentFields.push(`分数: ${first.score} vs ${current.score}`);
                            }
                            if (first.wuXingMatch !== current.wuXingMatch) {
                                inconsistentFields.push(`五行匹配: ${first.wuXingMatch} vs ${current.wuXingMatch}`);
                            }
                            break;
                        }
                    }

                    if (isConsistent) {
                        testResultsHtml += `<div class="test-result success">
                            ✅ 测试案例 ${index + 1} (${testCase.name}): 通过 - 所有${iterations}次计算结果一致
                        </div>`;
                    } else {
                        testResultsHtml += `<div class="test-result error">
                            ❌ 测试案例 ${index + 1} (${testCase.name}): 失败 - 计算结果不一致<br>
                            不一致字段: ${inconsistentFields.join(', ')}
                        </div>`;
                        allTestsPassed = false;

                        // 显示详细的调试信息
                        testResultsHtml += '<table><tr><th>次数</th><th>分数</th><th>五行匹配</th><th>需要五行</th><th>姓名五行</th></tr>';
                        detailedResults.forEach(detail => {
                            testResultsHtml += `<tr>
                                <td>${detail.iteration}</td>
                                <td>${detail.score}</td>
                                <td>${detail.wuXingMatch}</td>
                                <td>${detail.neededWuXing}</td>
                                <td>${detail.nameWuXing}</td>
                            </tr>`;
                        });
                        testResultsHtml += '</table>';
                    }

                    // 显示第一次计算的详细结果
                    const firstNameAnalysis = JSON.parse(firstResult.nameAnalysis);

                    testResultsHtml += `<div class="info">
                        <strong>详细结果 (${testCase.name}):</strong><br>
                        姓名分析分数: ${firstNameAnalysis.score}分<br>
                        五行匹配度: ${firstNameAnalysis.wuXingMatch}分<br>
                        需要补充的五行: ${detailedResults[0].neededWuXing}<br>
                        姓名五行分布: ${detailedResults[0].nameWuXing}
                    </div>`;
                    
                } catch (error) {
                    testResultsHtml += `<div class="test-result error">
                        ❌ 测试案例 ${index + 1} (${testCase.name}): 错误 - ${error.message}
                    </div>`;
                    allTestsPassed = false;
                }
            });
            
            // 总结
            if (allTestsPassed) {
                testResultsHtml += `<div class="test-result success">
                    <strong>🎉 所有测试通过！起名分值计算结果一致。</strong>
                </div>`;
            } else {
                testResultsHtml += `<div class="test-result error">
                    <strong>⚠️ 部分测试失败，存在计算结果不一致的问题。</strong>
                </div>`;
            }
            
            resultsDiv.innerHTML = testResultsHtml;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
